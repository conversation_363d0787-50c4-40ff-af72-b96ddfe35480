package com.chinamobile.iot.sc.controller;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.annotation.CodeValidMark;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.entity.user.EditFinancingInfoRequest;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.mode.QueryPartnerListParam;
import com.chinamobile.iot.sc.pojo.dto.ProvincePartnerDTO;
import com.chinamobile.iot.sc.pojo.entity.UserPartner;
import com.chinamobile.iot.sc.pojo.mapper.RoleMessageByAuthCodeDO;
import com.chinamobile.iot.sc.pojo.param.FollowUserParam;
import com.chinamobile.iot.sc.pojo.mapper.UserAndRoleByAuthDO;
import com.chinamobile.iot.sc.service.UserProductPipeService;
import com.chinamobile.iot.sc.service.impl.UserServiceImpl;
import com.chinamobile.iot.sc.vo.request.*;
import com.chinamobile.iot.sc.vo.response.UnifyUserDTO;
import com.chinamobile.iot.sc.vo.response.UnifyUserSyncDTO;
import com.chinamobile.iot.sc.vo.response.UserFollowListVO;
import com.chinamobile.iot.sc.vo.response.UserListItemVO;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static com.chinamobile.iot.sc.common.BaseConstant.*;

/**
 * @package: com.chinamobile.iot.sc.controller
 * @ClassName: UserLoginController
 * @description: 用户-登录校验Controller
 * @author: zyj
 * @create: 2021/9/2 18:26
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@RequestMapping("/base")
@RestController
public class UserController {
    @Resource
    private UserServiceImpl userService;

    @Resource
    private UserProductPipeService userProductPipeService;
    /**
     *@Description: 超级管理员登录
     *@Author: zyj
     */
    @CodeValidMark
    @PostMapping("/user/adminLogin") //超级管理员登录
    public BaseAnswer adminLogin(@RequestBody Request4Login request4Login){
        return userService.adminLogin(request4Login);
    }
    /**
     *@Description: 短信验证码登录
     *@Author: zyj
     */
    @CodeValidMark(validCode =false,validSmsCode = true)
    @PostMapping("/user/loginByMsCode") //普通用户登录(1.运营管理员、2.合作伙伴)
    public BaseAnswer loginByMSCode(@RequestParam String phone, @RequestParam(value = "userId",required = false)  String userId){
        return userService.loginByMSCode(phone,userId);
    }

    @PostMapping("/user/changeRole") //对于多角色用户，调整当前登录角色
    public BaseAnswer changeRole(@RequestBody @Validated Request4ChangeRole changeRole,
                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return userService.changeRole(changeRole.getUserId(),changeRole.getSystem(),loginIfo4Redis);
    }


    /**
     * 4A登录权限校验，并登录os系统返回token
     * @param request4aLoginParam
     * @return
     */
    @PostMapping("/user/token4aVerify")
    public BaseAnswer loginBy4aVerifyOperation(@RequestBody @Valid Request4aLoginParam request4aLoginParam){
      return   userService.loginBy4aVerify(request4aLoginParam.getToken4a(),request4aLoginParam.getAppAcctId());
    }

    /**
     * 短信登录先验证用户是否是主级别合作账号
     * @param phone
     * @return
     */
    @GetMapping("/user/verifyingUserLevels") //普通用户登录(1.运营管理员、2.合作伙伴)
    public BaseAnswer<List<Data4User>> verifyingUserLevels(@RequestParam String phone, HttpServletRequest request){
        return userService.chooseLordFromUser(phone,request);
    }

    /**
     *@Description: 用户登出
     *@Author: zyj
     */
    @PostMapping("/user/loginOut")
    public BaseAnswer<Void> loginOut(@RequestHeader(Constant.HEADER_KEY_USER_ID) String userId){
        return userService.loginOut(userId);
    }
    /**
     * 验证电话号码是否有效
     * @param phone
     * @return
     */
    @GetMapping("/user/phone/valid")
    public BaseAnswer<Boolean> validPhone(String phone){
        return userService.validPhone(phone);
    }
    /**
     * 修改密码
     * @param request4ModPwd
     * @param userId
     * @return
     */
    @Auth(authCode = {IOT_USER_OPERATOR, BaseConstant.IOT_USER_PARTNER,IOT_USER_PARTNER_LORD,IOT_USER_MANAGER})
    @PostMapping("/user/pwd/modify")
    public BaseAnswer<Void> pwdModify(@Validated @RequestBody Request4ModPwd request4ModPwd,
                                    @RequestHeader(value = Constant.HEADER_KEY_USER_ID,required = false) String userId){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return userService.pwdModify(request4ModPwd,userId,ip);
    }
    /**
     * 更换绑定号码
     * @param newPhone
     * @param newCode
     * @param phone 旧手机号
     * @param userId
     * @return
     */
    @Auth(authCode = {BaseConstant.ACCOUNT_INFO})
    @PostMapping("/user/phone/modify")
    @CodeValidMark(validCode = false, validNewSmsCode = true)
    public BaseAnswer<Void> modifyPhone(String newPhone, Integer newCode, String phone,
                                        @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId){

        return userService.modifyPhone(newPhone, newCode, phone, userId);
    }

    /**
     * 获取合作伙伴名称
     * @param userId
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = {IOT_USER_OPERATOR, BaseConstant.IOT_USER_PARTNER ,IOT_USER_PARTNER_LORD,IOT_USER_MANAGER})
    @GetMapping("/user/partner/name")
    public BaseAnswer getPartnerNameUser(@RequestHeader(Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return userService.getPartnerUserMessage(userId, loginIfo4Redis);
    }

    /**
     * 创建合作伙伴省管 查询已创建的合作伙伴名称
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping("/user/provincePartner/companyName")
    public BaseAnswer getProvincePartnerCompanyNameList(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return userService.getProvincePartnerCompanyName(loginIfo4Redis);
    }


    /**
     *@Description: 新增用户
     *@Author: zyj
     */
    @Auth(authCode = {IOT_USER_OPERATOR, BaseConstant.IOT_USER_PARTNER,IOT_USER_PARTNER_LORD,IOT_USER_MANAGER})
    @PostMapping("/user/add")
    public BaseAnswer addUser(@Validated @RequestBody Request4AddUser request4AddUser
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return userService.addUser(request4AddUser, userId, loginIfo4Redis,true,ip);
    }
    /**
     *@Description: 删除/注销用户
     *@Author: zyj
     */
    @Deprecated
    @Auth(authCode = {IOT_USER_OPERATOR, BaseConstant.IOT_USER_PARTNER,IOT_USER_PARTNER_LORD,IOT_USER_MANAGER})
    @PostMapping("/user/delete")
    public BaseAnswer deleteUser(@Validated @RequestBody Request4DeleteUser request4DeleteUser
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        Request4EnableUser request4EnableUser = new Request4EnableUser();
        BeanUtils.copyProperties(request4DeleteUser,request4EnableUser);
        request4EnableUser.setEnable(false);
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        userService.enableUser(request4EnableUser, loginIfo4Redis,ip);
        return new BaseAnswer();
    }

    /**
     * 用户账号停用
     * @param request4EnableUser
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = {IOT_USER_OPERATOR, BaseConstant.IOT_USER_PARTNER,IOT_USER_PARTNER_LORD,IOT_USER_MANAGER})
    @PostMapping("/user/enable")
    public BaseAnswer enableUser(@Validated @RequestBody Request4EnableUser request4EnableUser
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        userService.enableUser(request4EnableUser, loginIfo4Redis,ip);
        return new BaseAnswer();
    }




    /**
     * 用户注销（停用的用户可注销）
     */
    @PostMapping("/user/logoff")
    public BaseAnswer<Void> logoffUser(@Valid @RequestBody Request4LogoffUser request,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        userService.logoffUser(request,loginIfo4Redis);
        return new BaseAnswer<>();
    }


    /**
     *@Description: 编辑用户
     *@Author: zyj
     */
    @Auth(authCode = {IOT_USER_OPERATOR, BaseConstant.IOT_USER_PARTNER,IOT_USER_PARTNER_LORD,IOT_USER_MANAGER})
    @PostMapping("/user/edit")
    public BaseAnswer editUser(@Validated @RequestBody Request4EditUser request4EditUser
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return userService.editUser(request4EditUser, loginIfo4Redis,ip);
    }
    /**
     *@Description: 账号管理修改个人信息
     *@Author: zyj
     */
    @PostMapping("/user/edit/partner")
    public BaseAnswer editPartner(@Validated @RequestBody Request4EditPartner request
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return userService.editPartner(request, loginIfo4Redis,ip);
    }
    /**
     *@Description: 分页查询用户
     *@Author: zyj
     */
    @Auth(authCode = {IOT_USER_OPERATOR, BaseConstant.IOT_USER_PARTNER, IOT_USER_PARTNER_LORD,IOT_USER_MANAGER})
    @PostMapping("/user/page")
    @Deprecated
    public BaseAnswer<PageData<Data4User>> userPage(@Validated @RequestBody Request4UserPage request4UserPage
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return userService.userPage(request4UserPage, loginIfo4Redis);
    }
    /**
     *@Description: 查询用户详情
     *@Author: zyj
     */
    @GetMapping({"/user/info", "/internalAPI/user/info"})
    public BaseAnswer<Data4User> userInfo(@RequestHeader(Constant.HEADER_KEY_USER_ID) String userId){
        return userService.userInfo(userId,false,"");
    }

    @GetMapping("/user/info/byId")
    public BaseAnswer<Data4User> userInfoById(@RequestParam String userId){
        return userService.userInfo(userId,true,"");
    }

    @GetMapping("/user/info/byId/NoLog")
    public BaseAnswer<Data4User> userInfoByIdNoLog(@RequestParam(required = false) String userId){
        return userService.userInfoNoLog(userId,true);
    }

    @GetMapping("/user/info/web/byId")
    public BaseAnswer<Data4User> userInfoWebById(@RequestParam(value = "userId") String userId,@RequestParam(value = "operationModule") String operationModule){
        return userService.userInfo(userId,true,operationModule);
    }

    @GetMapping("/user/info/byPhone")
    public BaseAnswer<List<Data4User>> userInfoByPhone(@RequestParam String phone){
        return userService.userInfoByPhone(phone);
    }

    @GetMapping("/partner/info/byPhone")
    public BaseAnswer<List<Data4User>> partnerInfoByPhone(@RequestParam String phone){
        return userService.partnerInfoByPhone(phone);
    }

    @GetMapping("/partner/info/byPhoneList")
    public BaseAnswer<List<Data4User>> partnerInfoByPhoneList(@RequestParam List<String> phoneList){
        return userService.partnerInfoByPhoneList(phoneList);
    }

    @GetMapping("/partner/info/byId")
    public BaseAnswer<Data4User> partnerInfoById(@RequestParam String userId){
        return userService.partnerInfo(userId,true);
    }
    /**
     *@Description: 验证码校验（废弃）
     *@Author: zyj
     */
    @Deprecated
    @PostMapping("/captcha/valid")
    public BaseAnswer valid(@RequestParam String sessionId,@RequestParam String validCode){
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String  account = request.getParameter("userName")==null?request.getParameter("phone") :request.getParameter("userName") ;

        return userService.valid(sessionId, validCode,account);
    }

    /**
     * 获取主合作伙伴下的从合作用户id
     * @param userId
     * @return
     */
    @GetMapping("/user/down/list")
    public BaseAnswer<List<String>> getDownUserIdList(@RequestParam String userId){
      return   userService.getDownUserId(userId);
    }

    /**
     * 获取主合作伙伴下从伙伴信息
     * @param userId
     * @return
     */
    @GetMapping("/user/follow/list")
    public BaseAnswer<List<UserFollowListVO>> getFollowUserByHostIdList(@RequestParam String userId){
        return   userService.getFollowUserByMainUserId(userId);
    }


    /**
     * 获取商品配置时使用的合作伙伴从账号
     * @param followUserParam
     * @return
     */
    @GetMapping("/user/follow/user")
    public BaseAnswer<List<UserFollowListVO>> getFollowUserByMainUser(@Valid FollowUserParam followUserParam){
        return   userService.getFollowUserByMainUser(followUserParam);
    }

    /**
     * 通过从合作伙伴用户id查询主合作伙伴
     * @param userId
     * @return
     */
    @GetMapping("/user/primary/phone")
    public BaseAnswer<Data4User> getPrimaryUserPhone(@RequestParam String userId){
        return userService.getPrimaryUserPhones(userId);
    }

    /**
     * 查询合作伙伴信息 配置商品时使用
     * @param cooperatorId
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping("/user/cooperation/list")
    public BaseAnswer<List<Data4User>> listQueryUserByCooperatorIdMsg(@RequestParam(value = "cooperatorId",required = false) String cooperatorId,
                                                                      @RequestParam(value = "operate") String operate,
                                                                      @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
       return userService.listQueryUserByCooperatorId(cooperatorId,operate,loginIfo4Redis);
    }

    /**
     * 新增单个从账号一个主账号（个人调用接口）
     * @param roleId
     * @return
     */
    @Auth(authCode = {BaseConstant.IOT_USER_ROLE})
    @PostMapping("/user/add/primary")
    public BaseAnswer addDownOfPrimaryUser(@RequestParam String roleId){
        return userService.updateDownPartnerOne(roleId);
    }

    /**
     * 新增多个相同从合作伙伴的主账号（个人调用）
     * @param addUser
     * @return
     */
    @Auth(authCode = {BaseConstant.IOT_USER_ROLE})
    @PostMapping("/user/add/multiLord")
    public BaseAnswer insertMultiPartnerLordUser(@RequestBody @Valid Request4AddUser addUser){
        return userService.insertMultiPartnerLord(addUser);
    }

    /**
     *@Description: 登录后门接口。1.在OS系统内增加用户； 2.使用增加的用户登录，用户名是手机号，密码原文是 "手机号+wlx"进行MD5运算的结果。接口所需的是密码经过RSA加密后的密文
     */
    @CodeValidMark
    @PostMapping("/user/backLogin")
    public BaseAnswer backLogin(@RequestBody Request4Login request4Login){
        return userService.backLogin(request4Login);
    }


    /**
     * 超管运管查询所有主合作伙伴信息
     * @return
     */
    @Auth(authCode = {IOT_USER_PARTNER_LORD})
    @GetMapping("/user/getAllPrimaryUser")
    public BaseAnswer<List<UserPartner>> getAllPrimaryUser(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        List<UserPartner> allPrimaryUser = userService.getAllPrimaryUserCooperator(loginIfo4Redis);
        return new BaseAnswer<List<UserPartner>>().setData(allPrimaryUser);
    }


    /**
     * 新用户分页查询
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping("/user/list")
    public BaseAnswer<PageData<UserListItemVO>> getUserList(RequestUserListParam param,
                                                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return  userService.getUserList(param,loginIfo4Redis);
    }

    /**
     * 产品审批查询用户列表信息
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping("/user/product/list")
    public BaseAnswer<List<UserListItemVO>> getUserListAndProductOperationMessage(RequestUserListParam param,
                                                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return  userService.getUserListAndProductOperation(param,loginIfo4Redis);
    }


    /**
     * 根据权限编码查询拥有该权限的角色信息
     * @return
     */
    @GetMapping("/user/role/byAuthCode")
    public BaseAnswer<List<UserAndRoleByAuthDO>> getUserAndRoleMessageByAuthCodeList(){
        return  userService.getUserAndRoleMessageByAuthCode();
    }

    /**
     * 通过用户名从统一平台查询用户信息
     * @return
     */
    @GetMapping("/user/getUserFromUni")
    public BaseAnswer<UnifyUserDTO> getUniUser(@RequestParam("account") String account){
        UnifyUserDTO userDTO = userProductPipeService.packagingUnifyUserMessage(account);
        return new BaseAnswer<UnifyUserDTO>().setData(userDTO);
    }

    /**
     * 查询合作伙伴用户信息，不分页，内部调用使用
     * @return
     */
    @PostMapping("/partner/list")
    public BaseAnswer<List<Data4User>> getPartnerList(@RequestBody @Valid QueryPartnerListParam param){
        return  userService.getPartnerList(param);
    }

    /**
     * 查询合作伙伴-商机经理
     * @return
     */
    @GetMapping("/partner/business/list")
    public BaseAnswer<List<Data4User>> getPartnerListWeb(QueryPartnerListParam param){
        return  userService.getPartnerListWeb(param);
    }

    /**
     * 根据公司信息获取合作获取主账号信息
     * @param partnerName
     * @return
     */
    @GetMapping("/partner/getUserPartnerPrimaryByPartnerName")
    public BaseAnswer<Data4User> getUserPartnerPrimaryByPartnerName(@RequestParam(value = "partnerName") String partnerName){
        return  new BaseAnswer<Data4User>().setData(userService.getUserPartnerPrimaryByPartnerName(partnerName));
    }

    /**
     * 根据省份编码获取省公司的合作伙伴主账号信息
     * @param beId
     * @return
     */
    @GetMapping("/partner/getUserPartnerPrimaryByBeId")
    public BaseAnswer<Data4User> getUserPartnerPrimaryByBeId(@RequestParam(value = "beId") String beId){
        return  new BaseAnswer<Data4User>().setData(userService.getUserPartnerPrimaryByBeId(beId));
    }

    @GetMapping("/partner/info/byIds")
    public BaseAnswer<List<Data4User>> partnerInfoByIds(@RequestParam List<String> userIds){
        return userService.partnerInfo(userIds);
    }

    /**
     * 通过用户id集合查询用户信息
     * @param userIds
     * @return
     */
    @GetMapping("/user/info/byIds")
    public BaseAnswer<List<Data4User>> getUserInfoByUserIds(@RequestParam List<String> userIds){
        return userService.userInfoByUserIds(userIds);
    }

    /**统一平台用户推送同步接口*/
    @PostMapping("/unify/user/sync")
    public BaseAnswer<String> unifyUserSync(@RequestBody List<UnifyUserSyncDTO> users){
        userService.unifyUserSync(users);
        return BaseAnswer.success("成功");
    }

    /**条件查询主合作伙伴接口**/
    @GetMapping("/user/primary/list")
    public BaseAnswer<List<UserPartner>> userPrimaryList(@RequestParam String queryInfo){
        List<UserPartner> allPrimaryUser = userService.userPrimaryList(queryInfo);
        return new BaseAnswer<List<UserPartner>>().setData(allPrimaryUser);
    }

    /**
     * 询合作伙伴省公司用户通过省编码
     * @param beId
     * @return
     */
    @GetMapping("/user/province/list")
    public BaseAnswer<List<UserPartner>> getUserPartnerByProvinceList(@RequestParam String beId){
        List<UserPartner> userPartner = userService.getUserPartnerByProvince(beId);
        return new BaseAnswer<List<UserPartner>>().setData(userPartner);
    }


    /**修改产金用户信息**/
    @PostMapping ("/partner/financing/edit")
    public BaseAnswer<Void> editFinancingInfo(@RequestBody @Valid EditFinancingInfoRequest request){
        return userService.editFinancingInfo(request);
    };



    /**
     * 导入大屏用户(返回失败的二进制流)
     */
    @PostMapping("/user/importScreenUser")
    public void importScreenUser(MultipartFile file,
                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        userService.importScreenUser(file,loginIfo4Redis,ip);
    }

    /**
     * 导出合作伙伴用户信息
     * @param roleIds
     * @throws Exception
     */
    @GetMapping(value="/user/partner/export")
    public void exportOsPartnerMessageList(@RequestParam(value = "roleIds",required = false) List<String> roleIds) throws Exception {
        userService.exportOsPartnerMessage(roleIds);
    }

    /**
     * 导入合作伙伴用户()
     */
    @PostMapping("/user/partner/import")
    public void importPartnerUserMessage(MultipartFile file){
        userService.importPartnerUser(file);
    }


    /**
     * 获取新增主 从 省管合作伙伴查询合作伙伴名称
     */
    @GetMapping("/user/province/company")
    public BaseAnswer<List<ProvincePartnerDTO>> getProvinceCompanyListMessage(){
        List<ProvincePartnerDTO> provinceCompanyList = userService.getProvinceCompanyList();
        return new BaseAnswer<List<ProvincePartnerDTO>>().setData(provinceCompanyList);
    }


    /**
     * 根据角色id查询
     * @param roleId
     * @param beIds
     * @param locations
     * @return
     */
    @GetMapping("/user/province/businessList")
    public BaseAnswer<List<Data4User>> getUserProvinceBusinessList(@RequestParam(value = "roleId")String roleId,
                                                                   @RequestParam(value = "beIds",required = false) List<String> beIds,
                                                                   @RequestParam(value = "locations",required = false) List<String> locations){
        BaseAnswer<List<Data4User>> userProvinceBusiness = userService.getUserProvinceBusiness(roleId, beIds, locations);
        return userProvinceBusiness;
    }

    /**
     * 根据权限编码查询角色信息
     * @param authCodes
     * @return
     */
    @GetMapping("/user/roleMessage/byAuth")
    public BaseAnswer<List<RoleMessageByAuthCodeDO>> getRoleMessageByAuthCodeList(@RequestParam(value = "authCodes") List<String> authCodes){
        List<RoleMessageByAuthCodeDO> roleMessageByAuthCode = userService.getRoleMessageByAuthCode(authCodes);
        return new BaseAnswer<List<RoleMessageByAuthCodeDO>>().setData(roleMessageByAuthCode);
    }

    /**
     * 为大屏用户刷新gio权限
     * 1.从gio查询已注册gio的用户列表
     * 2.找到对应的大屏用户列表及其区域
     * 3.为gio用户设置区域权限
     */
    @PostMapping("/user/refreshGio")
    public BaseAnswer refreshGio(){


        return userService.refreshGio();
    }
}
