package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR> daiguojun
 * @date : 2023/10/31 10:13
 * @description: 云视讯服务开通状态
 **/
public class NoticeTypeConstant {


    /**
     * 短信通知类型:
     * 1-卡+x退换短信通知，
     * 2-代销云视讯服务开通失败短信通知
     * 3-行车卫士服务开通失败短信通知
     * 4-库存报警短信通知
     * 5-千里眼软件服务开通失败短信通知
     * 6-和易充业务软件服务开通失败短信通知
     * 7-行车卫士短信预警软件服务开通失败短信通知
     * 8-OneNET公有云服务软件服务开通失败短信通知
     * 9-和目业务平台软件服务开通失败短信通知
     * 10-服务类商品库存不足通知
     * 11-服务类商品库存警报通知
     * 12-OneCyber标品软件服务开通失败短信通知
     *  13-码号库存预警短信通知
     *  14-云瞳服务开通失败短信通知
     *  15-广西小程序软件服务开通短信通知
     *  16-江苏康养软件服务开通开通短信通知
     * */
    public static final Integer NOTICE_TYPE_KX = 1;

    public static final Integer NOTICE_TYPE_YSX_SERVICE = 2;

    public static final Integer NOTICE_TYPE_CAR_SECURITY_SERVICE = 3;

    public static final Integer NOTICE_TYPE_INVENTORY_WARNING = 4;
    public static final Integer NOTICE_TYPE_HYC_SOFTSERVICE_WARNING = 6;
    public static final Integer NOTICE_TYPE_QLY_SOFTSERVICE_WARNING = 5;

    public static final Integer NOTICE_TYPE_XCWS_SOFTSERVICE_WARNING = 7;
    public static final Integer NOTICE_TYPE_ONENET_SOFTSERVICE_WARNING = 8;
    public static final Integer NOTICE_TYPE_HM_SOFTSERVICE_WARNING = 9;
    public static final Integer NOTICE_TYPE_SERVICE_INSUFFICIENT_INVENTORY_WARNING = 10;
    public static final Integer NOTICE_TYPE_SERVICE_INVENTORY_ALARM_WARNING = 11;
    public static final Integer NOTICE_TYPE_ONECYBER_SOFTSERVICE_WARNING = 12;
    public static final Integer NOTICE_TYPE_CARD_INVENTORY_WARNING = 13;
    public static final Integer NOTICE_TYPE_YT_SOFTSERVICE_WARNING = 14;
    public static final Integer NOTICE_TYPE_GXMINI_SOFTSERVICE_WARNING = 15;
    public static final Integer NOTICE_TYPE_JSKY_SOFTSERVICE_WARNING = 16;
}
