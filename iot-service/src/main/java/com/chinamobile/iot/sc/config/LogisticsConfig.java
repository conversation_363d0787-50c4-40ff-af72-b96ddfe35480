package com.chinamobile.iot.sc.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/8/17 16:57
 * @description: 大屏物流配置
 **/
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix="logistics")
public class LogisticsConfig {

    private String customer;

    private String key;

    private String queryUrl;
}
