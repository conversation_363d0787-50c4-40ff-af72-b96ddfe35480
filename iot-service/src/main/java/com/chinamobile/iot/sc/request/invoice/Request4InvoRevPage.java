package com.chinamobile.iot.sc.request.invoice;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

/**
 * @package: com.chinamobile.iot.sc.request.invoice
 * @ClassName: Request4InvoRevPage
 * @description: 发票冲红分页查询请求
 * @author: zyj
 * @create: 2021/12/31 9:19
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class Request4InvoRevPage extends BasePageQuery {
    //冲红订单号（申请id）
    private String invoiceReverseId;
    //订单号
    private String orderId;
    //冲红申请状态
    private Integer status;

    /**
     * 请求流水号
     */
    private String orderSeq;

}
