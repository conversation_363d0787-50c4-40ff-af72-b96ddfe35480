package com.chinamobile.iot.sc.constant;

public enum PlatformClassEnum {
    Q("QIANLIYAN", "千里眼业务平台"),
    X("XINGCHEWEISHI", "行车卫士短信预警平台"),
    O("ONENET", "OneNET公有云平台"),
    H("HEMU", "和目业务平台"),
    OC("ONECYBER", "OneCyber5G 专网运营平台"),
    YT("YUNTONG", "云瞳业务平台"),
    GXMINI("GXMINI", "广西打印小程序业务平台"),
    JSKY("JSKY", "康养商城业务平台");;

    /**
     * 省名字
     */
    private final String platformName;
    /**
     * 省代码
     */
    private final String platformCode;

    PlatformClassEnum(String platformCode,String platformName ) {
        this.platformName = platformName;
        this.platformCode = platformCode;
    }

    public static String getPlatformCodeByPlatformName(String platformName) {
        for (PlatformClassEnum value : PlatformClassEnum.values()) {
            if (value.platformName.equals(platformName)) {
                return value.platformCode;
            }
        }
        return "UnKnown";
    }
    public String getPlatformCode() {
        return platformCode;
    }


}
