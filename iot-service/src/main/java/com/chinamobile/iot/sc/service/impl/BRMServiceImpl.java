package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.config.ProductFlowSmsConfig;
import com.chinamobile.iot.sc.dao.ext.BRMMapperExt;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.dto.BRMUploadDTO;
import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;
import com.chinamobile.iot.sc.service.BRMService;
import com.chinamobile.iot.sc.service.BaseSmsService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.SFTPUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.iot.sc.common.Constant.REDIS_KEY_BRM_UPLOAD_TXT_PARAM;

@Service
@Slf4j
public class BRMServiceImpl implements BRMService {
    @Value("${brm.ftp.name}")
    private String sftpUserName;
    @Value("${brm.ftp.password}")
    private String sftpPassword;
    @Value("${brm.ftp.host}")
    private String sftpHost;
    @Value("${brm.ftp.port}")
    private Integer sftpPort;
    @Value("${brm.ftp.workPath}")
    private String sftpWorkPath;
   
    @Value("${brm.sms.templateId:108393}")
    private String iopMessageId;

    @Resource
    private BRMMapperExt brmMapperExt;

    @Autowired
    private ProductFlowSmsConfig productFlowSmsConfig;

    @Autowired
    private BaseSmsService baseSmsService;
    // 获取iop校验报告

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void sftpUploadBRM(IOPUploadParam param) {
        List<String> phones = new ArrayList<>();
        phones.add("***********");
        phones.add("***********");
        phones.add("***********");
        List<String> successPhones = new ArrayList<>();
        // successPhones.add("***********");
        // successPhones.add("18323299093");
//        phones.add("***********");
        // 修改后代码（使用Calendar计算前一天）
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        String fileName = "IOTSTOCKINFO_" + DateTimeUtil.formatDate(calendar.getTime(), "yyyyMMdd") + ".txt";

        // 生成文件
        generateTxtFile(fileName, false);
        FileInputStream fileInputStream = null;

        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("连接brmsftp上传接口文件，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpWorkPath);
        try {
            if (sftpUtil.login()) {
                log.info("sftp连接成功！");
                log.info("开始上传文件原名:{}", fileName);
                File file = new File(fileName);
                fileInputStream = new FileInputStream(file);
                sftpUtil.upload(sftpWorkPath, fileName, fileInputStream);
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("datetime", DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_T));
                paramMap.put("result", "成功");
                //将文本名字写入redis中
                redisTemplate.opsForValue().set(REDIS_KEY_BRM_UPLOAD_TXT_PARAM, fileName, 2, TimeUnit.DAYS);
                // baseSmsService.sendMsg(successPhones, iopMessageId, paramMap);
            }
        } catch (Exception e) {
            log.error("SFTP上传文件失败！:{}", e.getMessage());
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("datetime", DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_T));
            paramMap.put("result", "失败");
            baseSmsService.sendMsg(phones, iopMessageId, paramMap);
            //将文本名字写入redis中  测试
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERR.getStateCode(),
                    StatusConstant.OSS_UPLOAD_ERR.getMessage());
        } finally {
            sftpUtil.logout();
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            log.info("登出sftp服务！");
        }

    }

    public void sendFtpFile(String fileName) {
        FileInputStream fileInputStream = null;
        List<String> phones = new ArrayList<>();
        phones.add("***********");
//        phones.add("***********");
        phones.add("***********");
        phones.add("***********");
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("连接brmsftp上传接口文件，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpWorkPath);
        try {
            if (sftpUtil.login()) {
                log.info("sftp连接成功！");
                log.info("开始上传文件原名:{}", fileName);
                File file = new File(fileName);
                fileInputStream = new FileInputStream(file);
                sftpUtil.upload(sftpWorkPath, fileName, fileInputStream);
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("date", "brm上传文件成功");
                paramMap.put("result", "成功");
                redisTemplate.opsForValue().set(REDIS_KEY_BRM_UPLOAD_TXT_PARAM, fileName, 2, TimeUnit.DAYS);
                // baseSmsService.sendMsg(successPhones, iopMessageId, paramMap);
            }
        } catch (Exception e) {
            log.error("SFTP上传文件失败！:{}", e.getMessage());
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("date", "brm上传文件失败");
            paramMap.put("result", "失败");
            baseSmsService.sendMsg(phones, iopMessageId, paramMap);
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERR.getStateCode(),
                    StatusConstant.OSS_UPLOAD_ERR.getMessage());
        } finally {
            sftpUtil.logout();
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            log.info("登出sftp服务！");
        }
    }

    public void generateTxtFile(String fileName, Boolean isWrite) {
        List<BRMUploadDTO> brmUploadDTOList = brmMapperExt.getBRMUploadList();
        List<BRMUploadDTO> brmUploadDTOListNew = new ArrayList<>();
        // 按想同纬度计算各自的可用库存量
        for (BRMUploadDTO dto : brmUploadDTOList) {
            BRMUploadDTO dto1 = new BRMUploadDTO();
            BeanUtils.copyProperties(dto, dto1);
            brmUploadDTOListNew.add(dto1);
        }
        Map<String, Long> map = new HashMap<>();
        // 先按照全省：省级+全地市的可用求和
        // 地市：省级+某地市的可用求和
        for (BRMUploadDTO dto : brmUploadDTOListNew) {

            if (dto.getRegion().equals("200")) {
                // 全省：省级+全地市的可用求和（多原子取最小值）
                for (BRMUploadDTO dto1 : brmUploadDTOList) {
                    Boolean isSame = isSame(dto, dto1);
                    // Boolean isSameT = isSameCardTemple(dto, dto1);
                    dto.setXStockNum(isSame ? dto.getXStockNum() + dto1.getXStockNum() : dto.getXStockNum());
                    dto.setXPickNum(isSame ? dto.getXPickNum() + dto1.getXPickNum() : dto.getXPickNum());
                    // dto.setCardStockNum(isSameT ? dto.getCardStockNum() + dto1.getCardStockNum()
                    // : dto.getCardStockNum());
                    // dto.setCardPickNum(isSameT ? dto.getCardPickNum() + dto1.getCardPickNum() :
                    // dto.getCardPickNum());
                }

            } else {
                // 全省+本市秋求和
                for (BRMUploadDTO dto1 : brmUploadDTOList) {
                    Boolean isSame = isSameCity(dto, dto1);
                    // Boolean isSameT = isSameCardTemple(dto, dto1);
                    dto.setXStockNum(isSame ? dto.getXStockNum() + dto1.getXStockNum() : dto.getXStockNum());
                    dto.setXPickNum(isSame ? dto.getXPickNum() + dto1.getXPickNum() : dto.getXPickNum());
                    // dto.setCardStockNum(isSameT ? dto.getCardStockNum() + dto1.getCardStockNum()
                    // : dto.getCardStockNum());
                    // dto.setCardPickNum(isSameT ? dto.getCardPickNum() + dto1.getCardPickNum() :
                    // dto.getCardPickNum());
                }

            }
        }
        // 省级同一维度原子取最小值 设备可用库存量
        List<BRMUploadDTO> brmUploadDTOListNew1 = new ArrayList<>();

        // 使用临时集合存储需要添加的元素，避免在遍历过程中直接修改目标集合
        List<BRMUploadDTO> tempAddList = new ArrayList<>();

        for (BRMUploadDTO dto : brmUploadDTOListNew) {
            if (dto.getRegion().equals("200")) {
                if (brmUploadDTOListNew1.isEmpty()) {
                    brmUploadDTOListNew1.add(dto);
                } else {
                    boolean isAdded = false; // 标记是否已处理当前 dto

                    for (BRMUploadDTO dto1 : brmUploadDTOListNew1) {
                        if (isSame(dto, dto1)) {
                            if (dto.getXStockNum() < dto1.getXStockNum()) {
                                dto1.setXStockNum(dto.getXStockNum()); // 更新最小值
                            }
                            dto1.setXPickNum(dto1.getXPickNum() + dto.getXPickNum());
                            isAdded = true; // 已处理，无需再添加
                        }
                    }

                    // 如果未找到匹配项，则将当前 dto 添加到临时集合
                    if (!isAdded) {

                        tempAddList.add(dto);
                    }
                }
            } else {
                BRMUploadDTO dto1 = new BRMUploadDTO();
                BeanUtils.copyProperties(dto, dto1);
                tempAddList.add(dto1); // 将非 "200" 区域的 DTO 添加到临时集合
            }
        }

        // 将临时集合中的元素批量添加到目标集合
        brmUploadDTOListNew1.addAll(tempAddList);

        // 将列表写入txt中

        try {
            File file = new File(fileName);
            if (!file.exists()) {
                file.createNewFile();
            }
            FileWriter fw = new FileWriter(file);
            BufferedWriter bw = new BufferedWriter(fw);

            // 写入标题行
            // 写入标题行，使用字段名称
            String headerLine = String.join("|",
                    "region", // 地区标识
                    "bossId", // 产商品编码
                    "vasName", // 产商品名称
                    "xModelName", // 设备型号
                    "xStockNum", // 设备可用库存
                    "xPickNum", // 设备预占量
                    "cardTempleCode", // 开卡模板编码
                    "cardTempleName", // 开卡模板名称
                    "cardVenderCode", // 卡服务商编码
                    "cardVenderName", // 卡服务商名称
                    "cardStockNum", // 卡可用库存
                    "cardPickNum", // 卡预占量
                    "saleNum" // 销量
            ) + "\r\n"; // 添加Windows换行符
            bw.write(headerLine);

            for (BRMUploadDTO dto : brmUploadDTOListNew1) {

                String line = String.join("|",
                        String.valueOf(dto.getRegion()), // 地区标识
                        dto.getBossId(), // 产商品编码
                        dto.getVasName(), // 产商品名称
                        dto.getXModelName(), // 设备型号
                        String.valueOf(dto.getXStockNum()), // 设备可用库存
                        String.valueOf(dto.getXPickNum()), // 设备预占量
                        dto.getCardTempleCode(), // 开卡模板编码
                        dto.getCardTempleName(), // 开卡模板名称
                        dto.getCardVenderCode(), // 卡服务商编码
                        dto.getCardVenderName(), // 卡服务商名称
                        String.valueOf(dto.getCardStockNum()), // 卡可用库存
                        String.valueOf(dto.getCardPickNum()), // 卡预占量
                        String.valueOf(dto.getSaleNum()) // 销量
                ) + "\r\n"; // 添加Windows换行符
                bw.write(line.toString());
            }
            //            log.info("txt内容:{}", bw);
            // 下载到本地
            if (isWrite) {
                bw.flush();
                bw.close();
            }
            log.info("txt文件生成成功！");
        } catch (Exception e) {
            log.error("生成txt文件失败：{}", e.getMessage());
        }


    }

    private static Boolean isSame(BRMUploadDTO now, BRMUploadDTO old) {
        if (now.getRegion().equals(old.getRegion()) && now.getId().equals(old.getId())) {
            return false;
        }
        if (!now.getCardTempleCode().equals(old.getCardTempleCode())) {
            return false;
        }
        if (!now.getCardVenderCode().equals(old.getCardVenderCode())) {
            return false;
        }
        if (!now.getBossId().equals(old.getBossId())) {
            return false;
        }
        if (!now.getXModelName().equals(old.getXModelName())) {
            return false;
        }
        return true;
    }

    private static Boolean isSameCity(BRMUploadDTO now, BRMUploadDTO old) {
        if (now.getId().equals(old.getId()) && now.getRegion().equals(old.getRegion())) {
            return false;
        }
        if (!now.getRegion().equals(old.getRegion()) && !old.getRegion().equals("200")) {
            return false;
        }
        if (!now.getCardTempleCode().equals(old.getCardTempleCode())) {
            return false;
        }
        if (!now.getCardVenderCode().equals(old.getCardVenderCode())) {
            return false;
        }
        if (!now.getBossId().equals(old.getBossId())) {
            return false;
        }
        if (!now.getXModelName().equals(old.getXModelName())) {
            return false;
        }
        return true;
    }
    // ... 现有代码 ...

    @Override
    public String exportBRMData() {
        // 使用Calendar计算日期
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1); // 默认导出前一天数据

        String dateStr = DateTimeUtil.formatDate(calendar.getTime(), "yyyyMMdd");
        String fileName = "IOTSTOCKINFO_" + dateStr + ".txt";

        // 生成文件
        generateTxtFile(fileName, true);
        //上传ftp
        sendFtpFile(fileName);
        log.info("BRM数据导出成功，文件名：{}", fileName);
        return fileName;
    }

    private static Boolean isSameCardTemple(BRMUploadDTO now, BRMUploadDTO old) {
        // 同一原子，同一省份，代表同一个BRMUploadDTO
        if (now.getRegion().equals(old.getRegion()) && now.getId().equals(old.getId())) {
            return false;
        }
        if (!now.getCardTempleCode().equals(old.getCardTempleCode())) {
            return false;
        }
        if (!now.getCardVenderCode().equals(old.getCardVenderCode())) {
            return false;
        }
        //
        // if(now.getRegion().equals(old.getBossId())){
        // return false;
        // }
        return true;
    }

    // 从字符串末尾截取指定长度的子字符串
    private static String reverseSubstring(String str, int length) {
        int startIndex = str.length() - length;
        if (startIndex < 0) {
            startIndex = 0;
        }

        return str.substring(startIndex);
    }

}
