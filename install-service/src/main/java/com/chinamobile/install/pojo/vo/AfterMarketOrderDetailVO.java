package com.chinamobile.install.pojo.vo;

import com.chinamobile.iot.sc.entity.UpResult;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 售后订单
 */
@Data
public class AfterMarketOrderDetailVO {
    /**
     * 售后订单ID
     */
    private String serviceOrderId;

    /**
     * 关联商品订单号
     */
    private String offeringOrderId;

    /**
     * 售后服务订单状态:
     * 1:待预约 -对应商城同步状态【1待预约（付款完成）】
     * 2:派单中-对应商城同步状态【2派单中（预约完成）】
     * 3:已预约
     * 4.已完结（成功）
     * 5.已完成（失败）
     * 6.已撤销
     * 7.交易完成-对应商城同步状态【3订单计收（订单同步至CMIoT成功后，同步本状态）】
     * 8.交易失败-对应商城同步状态【4退款完成】
     */
    private Integer status;

    /**
     * 关联商品订单状态
     */
    private Integer offeringOrderStatus;

    /**
     * 售后服务订单退款单状态
     */
    private Integer refundOrderStatus;

    /**
     * 订单总金额
     */
    private String totalPrice;

    /**
     * 预约提交类型，1：客户自主填入，2：超时系统自动提交
     */
    private Integer appointmentSubmitType;

    /**
     * 预约人姓名
     */
    private String appointmentName;

    /**
     * 预约人电话
     */
    private String appointmentPhone;

    /**
     * 预约地址
     */
    private String appointmentAddress;

    /**
     * 预约时间
     */
    private Date appointmentTime;

    /**
     * 售后订单商品信息
     */
    private List<AfterMarketOrderOfferingDetailVO> offeringItems;

    /**
     * 操作历史
     */
    private List<String> histories;

    /**
     * 商城主订单信息
     */
    private MallOrderInfoVO offeringOrderInfo;
    /**
     * 图片列表
     */
    private List<UpResult> imageList;
    /**
     * 客户经理
     */
    private AccountManager accountManager;
    /**
     * 分销员
     */
    private Distributor distributor;
    /**
     * 渠道商
     */
    private Agent agent;

    /**
     * 主订单附件
     */
    private List<OrderAttachmentVO> attachmentList;

    /**
     * 签到图片
     */
    private List<UpResult> signInImageList;

    /**
     * 物流信息列表
     */
    private List<LogisticsInfoVO> logisticsInfoList;

    /**
     * 设备信息列表
     */
    private List<DeviceInfoVO> deviceInfoList;

    @Data public static class AccountManager{
        private String employeeNum;
        private String customerManagerName;
        private String customerManagerPhone;
    }
    @Data public static class Distributor{
        private String distributorLevel;
        private String distributorPhone;
        private String distributorShareCode;
    }
    @Data public static class Agent{
        private String agentName;
        private String agentNumber;
        private String agentPhone;
    }

    @Data public static class DeviceInfoVO{

        /**
         * SN/IMEI号
         */
        private String snImei;

        /**
         * 物联卡号
         */
        private String cardNumber;
    }

}
