package com.chinamobile.install.controller;

import com.chinamobile.install.config.RedisLockConstant;
import com.chinamobile.install.dao.AfterMarketOrder2cInfoMapper;
import com.chinamobile.install.enums.AfterMarketOrderStatusEnum;
import com.chinamobile.install.pojo.entity.AfterMarketOrder2cInfo;
import com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfo;
import com.chinamobile.install.pojo.param.*;
import com.chinamobile.install.pojo.vo.AfterMarketOrderDetailVO;
import com.chinamobile.install.pojo.vo.AfterMarketOrderItemVO;
import com.chinamobile.install.pojo.vo.AfterMarketOrderStatsVO;
import com.chinamobile.install.pojo.vo.DocxTableParseVO;
import com.chinamobile.install.service.AfterMarketOrderH5Service;
import com.chinamobile.install.service.IAfterMarketOrderWebService;
import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Author: dgj
 * @Description:
 */
@RestController
@RequestMapping("/install")
@Slf4j
public class AfterMarketOrderWebController {

    @Resource
    private IAfterMarketOrderWebService afterMarketOrderWebService;

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private AfterMarketOrderH5Service afterMarketOrderH5Service;
    @Resource
    private AfterMarketOrder2cInfoMapper afterMarketOrder2cInfoMapper;

    /**
     * 售后订单查询
     *
     * @return
     */
    @GetMapping("/serviceOrder/list")
    public BaseAnswer<PageData<AfterMarketOrderItemVO>> getAfterMarketOrderList(@Valid AfterMarketOrderQueryParam param,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return afterMarketOrderWebService.getAfterMarketOrderList(param, loginIfo4Redis);
    }

    /**
     * (派单)售后订单派遣及重新派遣装维人员
     * 
     * @param orderDispatchParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/serviceOrder/dispatch")
    public BaseAnswer afterMarketOrderDispatchInstall(@RequestBody @Valid OrderDispatchParam orderDispatchParam,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return afterMarketOrderWebService.afterMarketOrderDispatch(orderDispatchParam, loginIfo4Redis, ip);
    }

    /**
     * 查询派单人员是否存在还在派单途中的订单数量
     * 
     * @param installUserId
     * @return
     */
    @GetMapping("/serviceOrder/installCount")
    public BaseAnswer<Long> getAfterMarketOrderByInstallUserIdNum(@RequestParam("installUserId") String installUserId) {
        Long count = afterMarketOrderWebService.getAfterMarketOrderByInstallUserId(installUserId);
        return new BaseAnswer<Long>().setData(count);
    }

    /**
     * 售后订单详情
     *
     * @return
     */
    @GetMapping("/serviceOrder/detail")
    public BaseAnswer<AfterMarketOrderDetailVO> getAfterMarketOrderDetail(
            @Valid AfterMarketOrderDetailParam afterMarketOrderDetailParam,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return afterMarketOrderWebService.getAfterMarketOrderDetail(afterMarketOrderDetailParam, loginIfo4Redis);
    }

    /**
     * 售后订单导出
     * 
     * @param param
     * @param response
     * @throws IOException
     */
    @GetMapping("/serviceOrder/export")
    public void export(AfterMarketOrderExportParam param,
                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
            HttpServletResponse response) throws IOException {
        afterMarketOrderWebService.export(param, loginIfo4Redis, response);
    }

    /**
     * 售后订单批量导入
     * 
     * @param file           导入文件
     * @param loginIfo4Redis 用户信息
     * @param request        请求
     * @param response       响应
     * @throws Exception 异常
     */
    @PostMapping("/serviceOrder/import")
    @Auth(authCode = { BaseConstant.IOT_ORDER_QUERY })
    public BaseAnswer<Void> importAfterMarketOrder(@RequestParam("file") MultipartFile file,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
            HttpServletRequest request,
            HttpServletResponse response) throws Exception {

            return afterMarketOrderWebService.importAfterMarketOrder(file.getInputStream(), loginIfo4Redis, request,
                    response);
    }

    /**
     * 修改售后订单信息
     * 
     * @param orderUpdateParam 订单修改参数
     * @param loginIfo4Redis   用户信息
     * @return 操作结果
     */
    @PostMapping("/serviceOrder/update")
    public BaseAnswer updateAfterMarketOrder(@RequestBody @Valid AfterMarketOrderUpdateParam orderUpdateParam,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return afterMarketOrderWebService.updateAfterMarketOrder(orderUpdateParam, loginIfo4Redis, ip);
    }

    /**
     * 关闭售后订单
     *
     * @param serviceOrderId 售后订单ID
     * @param loginIfo4Redis 用户信息
     * @return 操作结果
     */
    @GetMapping("/serviceOrder/close")
    public BaseAnswer closeAfterMarketOrder(@RequestParam("serviceOrderId") String serviceOrderId,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return afterMarketOrderWebService.closeAfterMarketOrder(serviceOrderId, loginIfo4Redis, ip);
    }

    /**
     * 更新售后订单关联服务商品的admin_cooperator_id
     * 其实就是分派
     * @param param 更新参数
     * @return 操作结果
     */
    @PostMapping("/serviceOrder/updateAdminCooperator")
    public BaseAnswer<Void> updateAdminCooperatorName(@RequestBody  UpdateAdminCooperatorParam param) {
        return afterMarketOrderWebService.updateAdminCooperatorName(param);
    }

    /**
     * 获取用户的工单统计信息
     *
     * @param loginIfo4Redis 用户信息
     * @return 工单统计信息
     */
    @GetMapping("/serviceOrder/stats")
    public BaseAnswer<AfterMarketOrderStatsVO> getAfterMarketOrderStats(
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return afterMarketOrderWebService.getAfterMarketOrderStats(loginIfo4Redis);
    }

    /**
<<<<<<< HEAD
     * 手动推送售后订单到省测
     *
     * @param param 手动推送参数
     * @param loginIfo4Redis 用户信息
     * @return 操作结果
     */
    @PostMapping("/serviceOrder/manualSyncToProvince")
    public BaseAnswer<Void> manualSyncToProvince(@RequestBody @Valid ManualSyncToProvinceParam param,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return afterMarketOrderWebService.manualSyncToProvince(param.getServiceOrderId(), loginIfo4Redis, ip);
    }
    /**
     * 更新交付状态，配置了权限
     *
     * @param deliveryParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/updateDeliveryStatus")
    public BaseAnswer updateDeliveryStatus(@RequestBody @Valid AfterMarketOrderH5DeliveryParam deliveryParam,
                                           @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = pubUpdateDeliveryStatus(deliveryParam, loginIfo4Redis);
        return baseAnswer;
    }
    private BaseAnswer pubUpdateDeliveryStatus(AfterMarketOrderH5DeliveryParam deliveryParam,
                                               LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        String userId = "";
        String serviceOrderId = deliveryParam.getServiceOrderId();
        AfterMarketOrder2cOfferingInfo order2cOfferingInfo = afterMarketOrderH5Service.getAfterOrderOfferingByUserIdAndServiceOrderId(userId, serviceOrderId);
        log.info("获取要交付订单信息：{}", order2cOfferingInfo);
        if (!Optional.ofNullable(order2cOfferingInfo).isPresent()) {
            baseAnswer.setStatus(BaseErrorConstant.AFTER_MARKET_INFO_NOT_EXIST);
            return baseAnswer;
        }
        AfterMarketOrder2cInfo order2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(order2cOfferingInfo.getServiceOrderId());
        if (order2cInfo == null) {
            baseAnswer.setStatus(BaseErrorConstant.AFTER_MARKET_INFO_NOT_EXIST);
            return baseAnswer;
        }
        if (order2cInfo.getStatus().intValue() != AfterMarketOrderStatusEnum.BEFORE_DISPATCHING.getStatus()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有【待分派】状态才可以交付");
        }

        Integer deliveryStatus = deliveryParam.getDeliveryStatus();

        if (deliveryStatus == 0) {
            //交付图片不能为空
            if (deliveryParam.getImageList().size() < 1) {
                baseAnswer.setStatus(BaseErrorConstant.AFTER_MARKET_DELIVERY_SERVICE_ATTACHMENTS_NOT_EMPTY);
                return baseAnswer;
            }
            String serviceCode = deliveryParam.getServiceCode();
            if (StringUtils.isEmpty(serviceCode)) {
                baseAnswer.setStatus(BaseErrorConstant.AFTER_MARKET_DELIVERY_SERVICE_CODE_NOT_EMPTY);
                return baseAnswer;
            }

            // 判断服务码是否正确
            String redisContractValue = RedisLockConstant.AFTER_ORDER_DISPATCH_INSTALL_REDIS_KEY.concat(serviceOrderId)
                    .concat(order2cOfferingInfo.getPresentSendOrderPhone());
            Object redisServiceCode = redisTemplate.opsForValue().get(redisContractValue);
            log.info("派单人员电话号码：{}，获取服务码:{}", order2cOfferingInfo.getPresentSendOrderPhone(), redisServiceCode);
            if (!Optional.ofNullable(redisServiceCode).isPresent()) {
                baseAnswer.setStatus(BaseErrorConstant.AFTER_MARKET_SERVICE_CODE_NOT_EXISTS);
                return baseAnswer;
            }
            if (!serviceCode.equals(redisServiceCode)) {
                baseAnswer.setStatus(BaseErrorConstant.AFTER_MARKET_SERVICE_CODE_NOT_CORRECT);
                return baseAnswer;
            }
        } else {
            String failureReason = deliveryParam.getFailureReason();
            if (StringUtils.isEmpty(failureReason)) {
                baseAnswer.setStatus(BaseErrorConstant.AFTER_MARKET_DELIVERY_FAILURE_REASON_NOT_EMPTY);
                return baseAnswer;
            }
        }

        Date deliverTime = order2cOfferingInfo.getDeliverTime();
        if (Optional.ofNullable(deliverTime).isPresent()) {
            baseAnswer.setStatus(BaseErrorConstant.AFTER_MARKET_DELIVERY_ALREADY_HANDLE);
            return baseAnswer;
        }
        afterMarketOrderWebService.updateDeliveryStatus(deliveryParam, order2cOfferingInfo, loginIfo4Redis);
        baseAnswer.setMessage("任务交付成功！");
        return baseAnswer;
    }
    /**
     * 装维订单直接交付
     *
     * @param deliveryParam   交付参数
     * @param loginIfo4Redis   用户信息
     * @return 操作结果
     */
    @PostMapping("/serviceOrder/delivery")
    public BaseAnswer<Void> updateAfterMarketOrder(@RequestBody @Valid AfterMarketOrderDeliveryParam deliveryParam,
                                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return afterMarketOrderWebService.deliveryAfterMarketOrder(deliveryParam, loginIfo4Redis);
    }

    /**
     * 解析xlsx文件中的表格，返回SN/IMEI和物联卡号两个字段的数组
     *
     * @param file           xlsx文件
     * @param loginIfo4Redis 用户信息
     * @param response       响应
     * @return 解析结果
     */
    @PostMapping("/serviceOrder/parseDocxTable")
    @Auth(authCode = { BaseConstant.IOT_ORDER_QUERY })
    public BaseAnswer<List<DocxTableParseVO>> parseDocxTable(
            @RequestPart("file") MultipartFile file,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
            HttpServletResponse response) {

        return afterMarketOrderWebService.parseDocxTable(file, loginIfo4Redis, response);
    }

}
