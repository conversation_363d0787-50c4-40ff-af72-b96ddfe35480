package com.chinamobile.install.service;

import com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfo;
import com.chinamobile.install.pojo.param.*;
import com.chinamobile.install.pojo.vo.AfterMarketOrderDetailVO;
import com.chinamobile.install.pojo.vo.AfterMarketOrderItemVO;
import com.chinamobile.install.pojo.vo.AfterMarketOrderStatsVO;
import com.chinamobile.install.pojo.vo.DocxTableParseVO;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * @Author: dgj
 * @Date: 2021/11/4 17:19
 * @Description: 售后订单服务类
 */
public interface IAfterMarketOrderWebService {

        /**
         * 售后订单派遣装维人员接口
         * 
         * @param orderDispatchParam
         * @param loginIfo4Redis
         * @return
         */
        BaseAnswer afterMarketOrderDispatch(OrderDispatchParam orderDispatchParam, LoginIfo4Redis loginIfo4Redis,
                        String ip);

        /**
         * 查询判断售后人员是否存在还在派单中的订单
         * 
         * @param installUserId
         * @return
         */
        Long getAfterMarketOrderByInstallUserId(String installUserId);

        /**
         * 售后订单列表
         * 
         * @param param
         * @param loginIfo4Redis
         * @return
         */
        BaseAnswer<PageData<AfterMarketOrderItemVO>> getAfterMarketOrderList(AfterMarketOrderQueryParam param,
                        LoginIfo4Redis loginIfo4Redis);

        /**
         * 售后订单详情
         * 
         * @param afterMarketOrderDetailParam
         * @param loginIfo4Redis
         * @return
         */
        BaseAnswer<AfterMarketOrderDetailVO> getAfterMarketOrderDetail(AfterMarketOrderDetailParam afterMarketOrderDetailParam,
                        LoginIfo4Redis loginIfo4Redis);

        /**
         * 售后订单导出
         * 
         * @param param
         * @param response
         * @throws IOException
         */
        void export(AfterMarketOrderExportParam param, LoginIfo4Redis loginIfo4Redis, HttpServletResponse response) throws IOException;

        /**
         * 售后订单批量导入
         * 
         * @param inputStream    输入流
         * @param loginIfo4Redis 用户信息
         * @param request        请求
         * @param response       响应
         * @throws Exception 异常
         */
        BaseAnswer<Void> importAfterMarketOrder(InputStream inputStream, LoginIfo4Redis loginIfo4Redis,
                        HttpServletRequest request,
                        HttpServletResponse response) throws Exception;

        /**
         * 修改售后订单信息
         * 
         * @param orderUpdateParam 订单修改参数
         * @param loginIfo4Redis   用户信息
         * @param ip               用户IP
         * @return 操作结果
         */
        BaseAnswer updateAfterMarketOrder(AfterMarketOrderUpdateParam orderUpdateParam, LoginIfo4Redis loginIfo4Redis,
                        String ip);

        /**
         * 关闭售后订单
         *
         * @param serviceOrderId 售后订单ID
         * @param loginIfo4Redis 用户信息
         * @param ip             用户IP
         * @return 操作结果
         */
        BaseAnswer closeAfterMarketOrder(String serviceOrderId, LoginIfo4Redis loginIfo4Redis, String ip);

        /**
         * 更新售后订单关联服务商品的admin_cooperator_id
         * @param param 更新参数
         * @return 操作结果
         */
        BaseAnswer<Void> updateAdminCooperatorName(UpdateAdminCooperatorParam param);

        /**
         * 获取用户工单统计信息
         *
         * @param loginIfo4Redis 用户信息
         * @return 工单统计信息
         */
        BaseAnswer<AfterMarketOrderStatsVO> getAfterMarketOrderStats(LoginIfo4Redis loginIfo4Redis);

        /**
<<<<<<< HEAD
         * 手动推送售后订单到省测
         *
         * @param serviceOrderId 售后服务订单ID
         * @param loginIfo4Redis 用户信息
         * @param ip 用户IP
         * @return 操作结果
         */
        BaseAnswer<Void> manualSyncToProvince(String serviceOrderId, LoginIfo4Redis loginIfo4Redis, String ip);
        /**
         * 更新交付状态
         * @param deliveryParam
         * @param afterMarketOrder2cOfferingInfo
         * @param loginIfo4Redis
         */
        void updateDeliveryStatus(AfterMarketOrderH5DeliveryParam deliveryParam,
                                  AfterMarketOrder2cOfferingInfo afterMarketOrder2cOfferingInfo,
                                  LoginIfo4Redis loginIfo4Redis);
        /**
         * 修改售后订单信息
         *
         * @param deliveryParam   交付参数
         * @param loginIfo4Redis   用户信息
         * @return 操作结果
         */
        BaseAnswer<Void> deliveryAfterMarketOrder(AfterMarketOrderDeliveryParam deliveryParam, LoginIfo4Redis loginIfo4Redis);

        /**
         * 解析xlsx文件中的表格
         *
         * @param file xlsx文件
         * @param loginIfo4Redis 用户信息
         * @param response 响应
         * @return 解析结果
         */
        BaseAnswer<List<DocxTableParseVO>> parseDocxTable(MultipartFile file, LoginIfo4Redis loginIfo4Redis, HttpServletResponse response);

}
