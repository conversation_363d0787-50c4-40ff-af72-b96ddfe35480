spring:
    cloud:
        nacos:
            discovery:
                namespace: public
                server-addr: 10.12.57.2:8848
    jackson:
        date-format: yyyy-MM-dd HH:mm:ss
        time-zone: GMT+8
    redis:
        cluster:
            nodes: 10.12.57.3:6381,10.12.57.4:6381,**********:6381
        password: app_!QAZxsw2
        pool:
            max-active: 5
            max-idle: 5
            max-wait: -1
            min-idle: 0
        timeout: 10000
jwt:
    secret: wh9bJefAzry
oauth2:
    clients:
        - registration-id: growingio-sso-client
          client-id: w7abzbuj3g4a
          client-secret: rnse59rkovo4nwxm4q9q
          authorization-grant-type:
              - authorization_code
              - refresh_token
          redirect-uri: "http://localhost:5000/callback"
          scopes:
              - unlimited