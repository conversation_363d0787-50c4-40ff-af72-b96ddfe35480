<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocalStorage Form Handler</title>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const token = localStorage.getItem('mallos-token');

            if (token) {
                // 创建并提交表单
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'login';

                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'token';
                input.value = token;

                form.appendChild(input);
                document.body.appendChild(form);
                form.submit();
            } else {
                // 重定向
                window.location.href = '/mallos/cockpit/#/';
            }
        });
    </script>
</head>
<body>
<p>Processing...</p>
</body>
</html>